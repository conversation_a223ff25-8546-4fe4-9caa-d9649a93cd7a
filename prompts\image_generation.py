"""
Image Generation Prompts
Centralized prompts for AI image generation with brand and content context.
"""

def build_image_generation_prompt(brand_profile, image_req, article_content=None) -> str:
    """Build the image generation prompt with all context."""
    
    # Brand information
    brand_section = f"""BRAND PROFILE:
- Brand: {brand_profile.metadata.brand_name}
- Description: {brand_profile.metadata.description}
- Tone: {', '.join(brand_profile.metadata.tone_keywords) if brand_profile.metadata.tone_keywords else 'Professional'}
- Mission: {brand_profile.metadata.mission_statement or 'Not specified'}"""

    # Color system
    colors = brand_profile.color_system
    color_section = f"""
BRAND COLORS (use these specific colors):
- Primary: {', '.join(colors.primary)}
- Secondary: {', '.join(colors.secondary)}
- Accent: {', '.join(colors.accent)}
- Neutral: {', '.join(colors.neutral)}"""

    # Typography
    typography_section = ""
    if brand_profile.typography:
        typography_section = f"""
TYPOGRAPHY:
- Primary Font: {brand_profile.typography.primary_font}
- Secondary Font: {brand_profile.typography.secondary_font or 'Not specified'}
- Font Style: {brand_profile.typography.font_style or 'Clean and modern'}"""

    # Image requirements
    image_section = f"""
IMAGE REQUIREMENTS:
- Title: "{image_req.title}"
- Purpose: {image_req.purpose}
- Content Focus: {image_req.content_focus}
- Key Points to Include: {', '.join(image_req.key_points)}
- Design Notes: {image_req.design_notes}"""

    # Article content context
    content_context = ""
    if article_content:
        # Article summary
        article_summary_section = ""
        if article_content.summary and len(article_content.summary.strip()) > 10:
            summary_text = article_content.summary
            article_summary_section = f"""
ARTICLE SUMMARY:
{summary_text}"""

        # Article key points
        article_key_points_section = ""
        if article_content.key_points:
            points_to_show = article_content.key_points[:8]
            article_key_points_section = f"""
ARTICLE KEY POINTS:
{chr(10).join(f"• {point}" for point in points_to_show)}"""

        # Call to action
        cta_section = ""
        if article_content.call_to_action:
            cta_section = f"""
CALL TO ACTION:
{article_content.call_to_action}"""

        # Section-specific context if this image is mapped to a specific section
        section_context_section = ""
        if image_req.section_reference and article_content.sections:
            # Find the referenced section
            referenced_section = None
            for section in article_content.sections:
                if section.title == image_req.section_reference:
                    referenced_section = section
                    break
            
            if referenced_section:
                section_context_section = f"""
SPECIFIC SECTION CONTEXT:
Section Title: {referenced_section.title}
Section Type: {referenced_section.section_type}
Section Key Points: {', '.join(referenced_section.key_points[:5])}
Section Themes: {', '.join(referenced_section.themes[:3])}
Section Content: {referenced_section.content[:400]}{"..." if len(referenced_section.content) > 400 else ""}"""

        # Raw content excerpt (if no specific section)
        raw_content_section = ""
        if (not section_context_section and 
            article_content.raw_content and 
            len(article_content.raw_content.strip()) > 50 and 
            len(article_content.raw_content) <= 800):
            raw_content_section = f"""
ORIGINAL CONTENT EXCERPT:
{article_content.raw_content[:600]}{"..." if len(article_content.raw_content) > 600 else ""}"""

        content_context = f"""
CONTENT CONTEXT:
- Article Title: {article_content.title}
- Main Themes: {', '.join(article_content.main_themes[:4]) if article_content.main_themes else 'General content'}
- Content Tone: {article_content.tone}
- Target Audience: {article_content.target_audience}{article_summary_section}{article_key_points_section}{cta_section}{section_context_section}{raw_content_section}"""

    # Content integration guidelines
    integration_guidelines = """
CONTENT INTEGRATION GUIDELINES:
- Draw inspiration from the article summary and key points to create relevant visual elements
- If this image is mapped to a specific section, focus primarily on that section's content and themes
- Ensure the image content aligns with the article's main themes and messages
- Use visual metaphors or elements that relate to the specific section or article's subject matter
- Consider incorporating relevant icons, symbols, or imagery that supports the section/article content
- Make sure the visual design reinforces the section's or article's key messages and target audience
- The image title should clearly reflect the specific section or topic being represented"""

    # Final prompt assembly
    prompt = f"""{brand_section}{color_section}{typography_section}{image_section}{content_context}

{integration_guidelines}

DESIGN REQUIREMENTS:
- Create a professional, on-brand image that represents the content effectively
- Use the specified brand colors prominently
- Include the title text prominently in the image
- Ensure text is readable and well-positioned within image boundaries
- Style should match the brand's tone and target audience
- Image should be suitable for digital/social media use
- Maintain high visual quality and professional appearance
- Avoid text cutoff - ensure all text stays within image boundaries

Create an image that effectively combines the brand identity with the content requirements."""

    return prompt

# Version tracking
IMAGE_GENERATION_VERSION = "v2.2"
IMAGE_GENERATION_CHANGELOG = """
v2.2: Enhanced section-specific context and improved text boundary guidelines
v2.1: Added section mapping support and enhanced content integration
v2.0: Added rich article context with summaries and key points
v1.0: Basic brand-focused image generation
"""
